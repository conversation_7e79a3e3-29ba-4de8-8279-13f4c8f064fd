apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: conversas-ai-worker-servicemonitor
  namespace: conversas-ai
  labels:
    app: conversas-ai-worker
    release: kube-prometheus-stack
spec:
  selector:
    matchLabels:
      app: conversas-ai-worker
  endpoints:
  - port: actuator
    path: /actuator/query/prometheus/components.DelayedQueueWorker.common.mensagens_a_processar_agora
    interval: 30s
    scrapeTimeout: 10s
